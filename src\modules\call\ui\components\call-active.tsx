import Link from "next/link";
import Image from "next/image";
import { Call<PERSON><PERSON><PERSON>s, SpeakerLayout, useCallStateHooks } from "@stream-io/video-react-sdk";
import { useTRPC } from "@/trpc/client";
import { useSuspenseQuery } from "@tanstack/react-query";
import { AgentParticipant } from "./agent-participant";
import { AgentStatusIndicator } from "./agent-status-indicator";

interface Props {
  onLeave: () => void;
  meetingName: string;
  meetingId: string;
}

export const CallActive = ({ onLeave, meetingName, meetingId }: Props) => {
  const trpc = useTRPC();
  const { useParticipants } = useCallStateHooks();
  const participants = useParticipants();

  // Get meeting data to find the agent
  const { data: meetingData } = useSuspenseQuery(
    trpc.meetings.getOne.queryOptions({ id: meetingId })
  );

  // Check if agent is in the call
  const agentParticipant = participants.find(p => p.userId === meetingData.agentId);
  const hasAgentJoined = !!agentParticipant;

  return (
    <div className="flex flex-col justify-between p-4 h-full text-white">
      <div className="bg-[#101213] rounded-full p-4 flex items-center gap-4">
        <Link
          href="/"
          className="flex items-center justify-center p-1 bg-white/10 rounded-full w-fit"
        >
          <Image src="/logo.svg" alt="Logo" width={22} height={22} />
        </Link>
        <h4 className="text-base">{meetingName}</h4>
        {meetingData.agent && (
          <div className="ml-auto">
            <AgentStatusIndicator
              agentId={meetingData.agentId}
              agentName={meetingData.agent.name}
            />
          </div>
        )}
      </div>

      <div className="flex-1 flex flex-col gap-4">
        <SpeakerLayout />

        {/* Agent Participant Display */}
        {meetingData.agent && (
          <div className="absolute top-20 right-4 w-64">
            <AgentParticipant
              agentId={meetingData.agentId}
              agentName={meetingData.agent.name}
            />
          </div>
        )}
      </div>

      <div className="bg-[#101213] rounded-full px-4">
        <CallControls onLeave={onLeave} />
      </div>
    </div>
  );
};
