'use client';

import { useEffect, useState } from 'react';
import { useCallStateHooks } from '@stream-io/video-react-sdk';
import { GeneratedAvatar } from '@/components/generated-avatar';
import { Badge } from '@/components/ui/badge';
import { BotIcon, MicIcon, MicOffIcon } from 'lucide-react';

interface AgentParticipantProps {
  agentId: string;
  agentName: string;
}

export const AgentParticipant = ({ agentId, agentName }: AgentParticipantProps) => {
  const { useParticipants } = useCallStateHooks();
  const participants = useParticipants();
  const [isAgentActive, setIsAgentActive] = useState(false);
  const [isAgentSpeaking, setIsAgentSpeaking] = useState(false);

  // Find the agent participant
  const agentParticipant = participants.find(p => p.userId === agentId);

  useEffect(() => {
    if (agentParticipant) {
      setIsAgentActive(true);
      
      // Monitor agent's audio state
      const audioTrack = agentParticipant.audioStream;
      if (audioTrack) {
        // Simple audio level detection (you might want to implement more sophisticated detection)
        const checkAudioLevel = () => {
          setIsAgentSpeaking(agentParticipant.isSpeaking || false);
        };
        
        const interval = setInterval(checkAudioLevel, 100);
        return () => clearInterval(interval);
      }
    } else {
      setIsAgentActive(false);
      setIsAgentSpeaking(false);
    }
  }, [agentParticipant, agentId]);

  if (!isAgentActive) {
    return (
      <div className="bg-gray-900 rounded-lg p-4 flex flex-col items-center justify-center min-h-[200px] border-2 border-dashed border-gray-600">
        <GeneratedAvatar
          seed={agentName}
          variant="botttsNeutral"
          className="size-16 mb-3 opacity-50"
        />
        <p className="text-gray-400 text-sm text-center">
          {agentName} will join when the meeting starts
        </p>
        <Badge variant="outline" className="mt-2 text-xs">
          <BotIcon className="w-3 h-3 mr-1" />
          AI Agent
        </Badge>
      </div>
    );
  }

  return (
    <div className={`bg-gray-900 rounded-lg p-4 flex flex-col items-center justify-center min-h-[200px] border-2 transition-all duration-200 ${
      isAgentSpeaking ? 'border-green-500 shadow-lg shadow-green-500/20' : 'border-gray-700'
    }`}>
      <div className="relative">
        <GeneratedAvatar
          seed={agentName}
          variant="botttsNeutral"
          className={`size-20 mb-3 transition-all duration-200 ${
            isAgentSpeaking ? 'ring-4 ring-green-500 ring-opacity-50' : ''
          }`}
        />
        {isAgentSpeaking && (
          <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1">
            <MicIcon className="w-3 h-3 text-white" />
          </div>
        )}
        {!isAgentSpeaking && (
          <div className="absolute -bottom-1 -right-1 bg-gray-600 rounded-full p-1">
            <MicOffIcon className="w-3 h-3 text-white" />
          </div>
        )}
      </div>
      
      <h3 className="text-white font-medium text-center mb-2">{agentName}</h3>
      
      <div className="flex flex-col items-center gap-2">
        <Badge variant="outline" className="text-xs">
          <BotIcon className="w-3 h-3 mr-1" />
          AI Agent
        </Badge>
        
        {isAgentSpeaking && (
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></div>
            <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse delay-75"></div>
            <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse delay-150"></div>
            <span className="text-green-400 text-xs ml-2">Speaking</span>
          </div>
        )}
      </div>
    </div>
  );
};
