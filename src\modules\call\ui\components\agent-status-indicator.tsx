'use client';

import { useEffect, useState } from 'react';
import { useCallStateHooks } from '@stream-io/video-react-sdk';
import { Badge } from '@/components/ui/badge';
import { BotIcon, WifiIcon, WifiOffIcon } from 'lucide-react';

interface AgentStatusIndicatorProps {
  agentId: string;
  agentName: string;
}

export const AgentStatusIndicator = ({ agentId, agentName }: AgentStatusIndicatorProps) => {
  const { useParticipants } = useCallStateHooks();
  const participants = useParticipants();
  const [agentStatus, setAgentStatus] = useState<'waiting' | 'connecting' | 'active' | 'disconnected'>('waiting');

  useEffect(() => {
    const agentParticipant = participants.find(p => p.userId === agentId);
    
    if (agentParticipant) {
      // Agent is in the call
      if (agentParticipant.connectionQuality === 'excellent' || 
          agentParticipant.connectionQuality === 'good') {
        setAgentStatus('active');
      } else {
        setAgentStatus('connecting');
      }
    } else {
      // Agent is not in the call yet
      setAgentStatus('waiting');
    }
  }, [participants, agentId]);

  const getStatusConfig = () => {
    switch (agentStatus) {
      case 'active':
        return {
          color: 'bg-green-500',
          text: 'Agent Active',
          icon: <WifiIcon className="w-3 h-3" />,
          variant: 'default' as const
        };
      case 'connecting':
        return {
          color: 'bg-yellow-500',
          text: 'Agent Connecting',
          icon: <WifiIcon className="w-3 h-3 animate-pulse" />,
          variant: 'secondary' as const
        };
      case 'waiting':
        return {
          color: 'bg-gray-500',
          text: 'Waiting for Agent',
          icon: <WifiOffIcon className="w-3 h-3" />,
          variant: 'outline' as const
        };
      case 'disconnected':
        return {
          color: 'bg-red-500',
          text: 'Agent Disconnected',
          icon: <WifiOffIcon className="w-3 h-3" />,
          variant: 'destructive' as const
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Badge variant={config.variant} className="flex items-center gap-2">
      <BotIcon className="w-3 h-3" />
      {config.icon}
      <span className="text-xs">{agentName}</span>
      <div className={`w-2 h-2 rounded-full ${config.color} ${agentStatus === 'active' ? 'animate-pulse' : ''}`}></div>
    </Badge>
  );
};
