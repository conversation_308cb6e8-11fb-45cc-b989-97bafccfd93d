{"name": "meetai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev:webhook": "ngrok http --url=precise-funky-hagfish.ngrok-free.app 3000"}, "dependencies": {"@dicebear/collection": "^9.2.2", "@dicebear/core": "^9.2.2", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stream-io/node-sdk": "^0.4.24", "@stream-io/openai-realtime-api": "^0.2.0", "@stream-io/video-react-sdk": "^1.18.0", "@tanstack/react-query": "^5.76.1", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.1.2", "@trpc/server": "^11.1.2", "@trpc/tanstack-react-query": "^11.1.2", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.6.0", "humanize-duration": "^3.32.2", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "openai": "^5.8.1", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "server-only": "^0.0.1", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "vaul": "^1.1.2", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/humanize-duration": "^3.27.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}